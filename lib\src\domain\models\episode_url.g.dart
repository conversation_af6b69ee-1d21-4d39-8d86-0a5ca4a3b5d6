// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'episode_url.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$EpisodeUrlImpl _$$EpisodeUrlImplFromJson(Map<String, dynamic> json) =>
    _$EpisodeUrlImpl(
      headers: (json['headers'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      sources: (json['sources'] as List<dynamic>?)
          ?.map((e) => Source.fromJson(e as Map<String, dynamic>))
          .toList(),
      subtitles: (json['subtitles'] as List<dynamic>?)
          ?.map((e) => Subtitle.fromJson(e as Map<String, dynamic>))
          .toList(),
      intro: json['intro'] == null
          ? null
          : Intro.fromJson(json['intro'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$EpisodeUrlImplToJson(_$EpisodeUrlImpl instance) =>
    <String, dynamic>{
      'headers': instance.headers,
      'sources': instance.sources,
      'subtitles': instance.subtitles,
      'intro': instance.intro,
    };

_$SourceImpl _$$SourceImplFromJson(Map<String, dynamic> json) => _$SourceImpl(
      url: json['url'] as String?,
      quality: json['quality'] as String?,
      isM3U8: json['isM3U8'] as bool?,
    );

Map<String, dynamic> _$$SourceImplToJson(_$SourceImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'quality': instance.quality,
      'isM3U8': instance.isM3U8,
    };

_$SubtitleImpl _$$SubtitleImplFromJson(Map<String, dynamic> json) =>
    _$SubtitleImpl(
      url: json['url'] as String?,
      lang: json['lang'] as String?,
    );

Map<String, dynamic> _$$SubtitleImplToJson(_$SubtitleImpl instance) =>
    <String, dynamic>{
      'url': instance.url,
      'lang': instance.lang,
    };

_$IntroImpl _$$IntroImplFromJson(Map<String, dynamic> json) => _$IntroImpl(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$IntroImplToJson(_$IntroImpl instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
    };

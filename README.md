# Aniggo
Anime streaming app built with [Flutter](https://github.com/flutter/flutter) and [Consumet](https://github.com/consumet).

## Features
<ul>
  <li>Multiple provider like zoro, gogo etc</li>
  <li>Advanced search with filters</li>
  <li>Add to favourites</li>
  <li>Mark episodes as watched</li>
  <li>Dark and light mode with multiple accent colors</li>
</ul>

## Screenshots
<table>
  <tr>
    <td>Home Screen</td>
    <td>Anime Detail</td>
    <td>Anime List</td>
  </tr>
  <tr>
    <td><img src="screenshots/flutter_01.png" width=250></td>
    <td><img src="screenshots/flutter_06.png" width=250></td>
    <td><img src="screenshots/flutter_02.png" width=250></td>
  </tr>
  <tr>
    <td>Filter</td>
    <td>Favourites</td>
    <td>Settings</td>
  </tr>
  <tr>
    <td><img src="screenshots/flutter_03.png" width=250></td>
    <td><img src="screenshots/flutter_04.png" width=250></td>
    <td><img src="screenshots/flutter_05.png" width=250></td>
  </tr>
</table>
<br>

## Screenshots Dark mode
<table>
  <tr>
    <td>Home Screen</td>
    <td>Anime List</td>
    <td>Anime Detail</td>
  </tr>
  <tr>
    <td><img src="screenshots/flutter_07.png" width=250></td>
    <td><img src="screenshots/flutter_09.png" width=250></td>
    <td><img src="screenshots/flutter_08.png" width=250></td>
  </tr>
  <tr>
    <td>Filter</td>
    <td>Favourites</td>
    <td>Settings</td>
  </tr>
  <tr>
    <td><img src="screenshots/flutter_10.png" width=250></td>
    <td><img src="screenshots/flutter_12.png" width=250></td>
    <td><img src="screenshots/flutter_11.png" width=250></td>
  </tr>
</table>

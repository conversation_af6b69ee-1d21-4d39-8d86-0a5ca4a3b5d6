name: aniggo
description: Anime streaming app in flutter.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=2.18.2 <3.0.0"

dependencies:
  video_player: ^2.8.1
  cached_network_image: ^3.3.0
  carousel_slider: ^5.0.0
  connectivity_plus: ^6.0.5
  dio: ^5.4.0
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.4.9
  freezed_annotation: ^3.0.0
  hive_flutter: ^1.1.0
  infinite_scroll_pagination: ^4.0.0
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  json_annotation: ^4.8.1
  path_provider: ^2.1.2
  readmore: ^3.0.0
  url_launcher: ^6.2.2

dev_dependencies:
  build_runner: ^2.4.7
  change_app_package_name: ^1.3.0
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  freezed: ^3.0.0-0.0.dev
  hive_generator: ^2.0.1
  isar_generator: ^3.1.0+1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true

  # assets:
  # - assets/images/
  fonts:
    - family: GoogleSans
      fonts:
        - asset: assets/font/GoogleSans-Regular.ttf
          weight: 400
        - asset: assets/font/GoogleSans-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/font/GoogleSans-Medium.ttf
          weight: 500
        - asset: assets/font/GoogleSans-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/font/GoogleSans-Bold.ttf
          weight: 700
        - asset: assets/font/GoogleSans-BoldItalic.ttf
          weight: 700
          style: italic

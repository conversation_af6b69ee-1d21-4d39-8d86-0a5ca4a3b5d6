name: aniggo
description: Anime streaming app in flutter.

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ">=2.18.2 <3.0.0"

dependencies:
  better_player:
    git:
      url: https://github.com/tintran-dev/betterplayer
  cached_network_image: ^3.2.2
  carousel_slider: ^4.2.1
  connectivity_plus: ^3.0.2
  dio: ^4.0.6
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.1.3
  freezed_annotation: ^2.2.0
  hive_flutter: ^1.1.0
  infinite_scroll_pagination: ^3.2.0
  isar: ^3.0.5
  isar_flutter_libs: ^3.0.4
  json_annotation: ^4.7.0
  path_provider: ^2.0.11
  readmore: ^2.2.0
  url_launcher: ^6.1.6

dev_dependencies:
  build_runner: ^2.3.3
  change_app_package_name: ^1.1.0
  flutter_lints: ^2.0.0
  flutter_test:
    sdk: flutter
  freezed: ^2.1.1
  hive_generator: ^2.0.0
  isar_generator: ^3.0.5
  json_serializable: ^6.5.1

flutter:
  uses-material-design: true

  # assets:
  # - assets/images/
  fonts:
    - family: GoogleSans
      fonts:
        - asset: assets/font/GoogleSans-Regular.ttf
          weight: 400
        - asset: assets/font/GoogleSans-Italic.ttf
          weight: 400
          style: italic
        - asset: assets/font/GoogleSans-Medium.ttf
          weight: 500
        - asset: assets/font/GoogleSans-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/font/GoogleSans-Bold.ttf
          weight: 700
        - asset: assets/font/GoogleSans-BoldItalic.ttf
          weight: 700
          style: italic

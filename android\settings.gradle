pluginManagement {
    def localProperties = new Properties()
    def localPropertiesFile = new File(rootProject.projectDir, "local.properties")
    // Đ<PERSON><PERSON> bảo localProperties được load đúng cách
    if (localPropertiesFile.exists()) {
        localPropertiesFile.withReader("UTF-8") { reader -> localProperties.load(reader) }
    }

    def flutterSdkPath = localProperties.getProperty("flutter.sdk")
    assert flutterSdkPath != null, "flutter.sdk not set in local.properties. Add a line like 'flutter.sdk=/path/to/flutter/sdk' to android/local.properties."

    includeBuild "$flutterSdkPath/packages/flutter_tools/gradle"

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.2.0" apply false
    id "org.jetbrains.kotlin.android" version "1.9.20" apply false
}

include ':app'

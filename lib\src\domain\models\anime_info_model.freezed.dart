// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'anime_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AnimeInfoModel _$AnimeInfoModelFromJson(Map<String, dynamic> json) {
  return _AnimeInfoModel.fromJson(json);
}

/// @nodoc
mixin _$AnimeInfoModel {
  String? get id => throw _privateConstructorUsedError;
  AnimeTitle? get title => throw _privateConstructorUsedError;
  int? get malId => throw _privateConstructorUsedError;
  List<String>? get synonyms => throw _privateConstructorUsedError;
  bool? get isLicensed => throw _privateConstructorUsedError;
  bool? get isAdult => throw _privateConstructorUsedError;
  String? get countryOfOrigin => throw _privateConstructorUsedError;
  AnimeTrailer? get trailer => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  int? get popularity => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;
  String? get cover => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  int? get releaseDate => throw _privateConstructorUsedError;
  StartDate? get startDate => throw _privateConstructorUsedError;
  StartDate? get endDate => throw _privateConstructorUsedError;
  int? get totalEpisodes => throw _privateConstructorUsedError;
  int? get rating => throw _privateConstructorUsedError;
  int? get duration => throw _privateConstructorUsedError;
  List<String>? get genres => throw _privateConstructorUsedError;
  String? get season => throw _privateConstructorUsedError;
  List<String>? get studios => throw _privateConstructorUsedError;
  String? get subOrDub => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  List<Recommendation>? get recommendations =>
      throw _privateConstructorUsedError;
  List<Character>? get characters => throw _privateConstructorUsedError;
  List<Relation>? get relations => throw _privateConstructorUsedError;
  List<Episode>? get episodes => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AnimeInfoModelCopyWith<AnimeInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AnimeInfoModelCopyWith<$Res> {
  factory $AnimeInfoModelCopyWith(
          AnimeInfoModel value, $Res Function(AnimeInfoModel) then) =
      _$AnimeInfoModelCopyWithImpl<$Res, AnimeInfoModel>;
  @useResult
  $Res call(
      {String? id,
      AnimeTitle? title,
      int? malId,
      List<String>? synonyms,
      bool? isLicensed,
      bool? isAdult,
      String? countryOfOrigin,
      AnimeTrailer? trailer,
      String? image,
      int? popularity,
      String? color,
      String? cover,
      String? description,
      String? status,
      int? releaseDate,
      StartDate? startDate,
      StartDate? endDate,
      int? totalEpisodes,
      int? rating,
      int? duration,
      List<String>? genres,
      String? season,
      List<String>? studios,
      String? subOrDub,
      String? type,
      List<Recommendation>? recommendations,
      List<Character>? characters,
      List<Relation>? relations,
      List<Episode>? episodes});

  $AnimeTitleCopyWith<$Res>? get title;
  $AnimeTrailerCopyWith<$Res>? get trailer;
  $StartDateCopyWith<$Res>? get startDate;
  $StartDateCopyWith<$Res>? get endDate;
}

/// @nodoc
class _$AnimeInfoModelCopyWithImpl<$Res, $Val extends AnimeInfoModel>
    implements $AnimeInfoModelCopyWith<$Res> {
  _$AnimeInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? malId = freezed,
    Object? synonyms = freezed,
    Object? isLicensed = freezed,
    Object? isAdult = freezed,
    Object? countryOfOrigin = freezed,
    Object? trailer = freezed,
    Object? image = freezed,
    Object? popularity = freezed,
    Object? color = freezed,
    Object? cover = freezed,
    Object? description = freezed,
    Object? status = freezed,
    Object? releaseDate = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? totalEpisodes = freezed,
    Object? rating = freezed,
    Object? duration = freezed,
    Object? genres = freezed,
    Object? season = freezed,
    Object? studios = freezed,
    Object? subOrDub = freezed,
    Object? type = freezed,
    Object? recommendations = freezed,
    Object? characters = freezed,
    Object? relations = freezed,
    Object? episodes = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      synonyms: freezed == synonyms
          ? _value.synonyms
          : synonyms // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isLicensed: freezed == isLicensed
          ? _value.isLicensed
          : isLicensed // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdult: freezed == isAdult
          ? _value.isAdult
          : isAdult // ignore: cast_nullable_to_non_nullable
              as bool?,
      countryOfOrigin: freezed == countryOfOrigin
          ? _value.countryOfOrigin
          : countryOfOrigin // ignore: cast_nullable_to_non_nullable
              as String?,
      trailer: freezed == trailer
          ? _value.trailer
          : trailer // ignore: cast_nullable_to_non_nullable
              as AnimeTrailer?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      popularity: freezed == popularity
          ? _value.popularity
          : popularity // ignore: cast_nullable_to_non_nullable
              as int?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      releaseDate: freezed == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as StartDate?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as StartDate?,
      totalEpisodes: freezed == totalEpisodes
          ? _value.totalEpisodes
          : totalEpisodes // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      genres: freezed == genres
          ? _value.genres
          : genres // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      season: freezed == season
          ? _value.season
          : season // ignore: cast_nullable_to_non_nullable
              as String?,
      studios: freezed == studios
          ? _value.studios
          : studios // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subOrDub: freezed == subOrDub
          ? _value.subOrDub
          : subOrDub // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendations: freezed == recommendations
          ? _value.recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<Recommendation>?,
      characters: freezed == characters
          ? _value.characters
          : characters // ignore: cast_nullable_to_non_nullable
              as List<Character>?,
      relations: freezed == relations
          ? _value.relations
          : relations // ignore: cast_nullable_to_non_nullable
              as List<Relation>?,
      episodes: freezed == episodes
          ? _value.episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as List<Episode>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AnimeTitleCopyWith<$Res>? get title {
    if (_value.title == null) {
      return null;
    }

    return $AnimeTitleCopyWith<$Res>(_value.title!, (value) {
      return _then(_value.copyWith(title: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AnimeTrailerCopyWith<$Res>? get trailer {
    if (_value.trailer == null) {
      return null;
    }

    return $AnimeTrailerCopyWith<$Res>(_value.trailer!, (value) {
      return _then(_value.copyWith(trailer: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $StartDateCopyWith<$Res>? get startDate {
    if (_value.startDate == null) {
      return null;
    }

    return $StartDateCopyWith<$Res>(_value.startDate!, (value) {
      return _then(_value.copyWith(startDate: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $StartDateCopyWith<$Res>? get endDate {
    if (_value.endDate == null) {
      return null;
    }

    return $StartDateCopyWith<$Res>(_value.endDate!, (value) {
      return _then(_value.copyWith(endDate: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$AnimeInfoModelImplCopyWith<$Res>
    implements $AnimeInfoModelCopyWith<$Res> {
  factory _$$AnimeInfoModelImplCopyWith(_$AnimeInfoModelImpl value,
          $Res Function(_$AnimeInfoModelImpl) then) =
      __$$AnimeInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      AnimeTitle? title,
      int? malId,
      List<String>? synonyms,
      bool? isLicensed,
      bool? isAdult,
      String? countryOfOrigin,
      AnimeTrailer? trailer,
      String? image,
      int? popularity,
      String? color,
      String? cover,
      String? description,
      String? status,
      int? releaseDate,
      StartDate? startDate,
      StartDate? endDate,
      int? totalEpisodes,
      int? rating,
      int? duration,
      List<String>? genres,
      String? season,
      List<String>? studios,
      String? subOrDub,
      String? type,
      List<Recommendation>? recommendations,
      List<Character>? characters,
      List<Relation>? relations,
      List<Episode>? episodes});

  @override
  $AnimeTitleCopyWith<$Res>? get title;
  @override
  $AnimeTrailerCopyWith<$Res>? get trailer;
  @override
  $StartDateCopyWith<$Res>? get startDate;
  @override
  $StartDateCopyWith<$Res>? get endDate;
}

/// @nodoc
class __$$AnimeInfoModelImplCopyWithImpl<$Res>
    extends _$AnimeInfoModelCopyWithImpl<$Res, _$AnimeInfoModelImpl>
    implements _$$AnimeInfoModelImplCopyWith<$Res> {
  __$$AnimeInfoModelImplCopyWithImpl(
      _$AnimeInfoModelImpl _value, $Res Function(_$AnimeInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? malId = freezed,
    Object? synonyms = freezed,
    Object? isLicensed = freezed,
    Object? isAdult = freezed,
    Object? countryOfOrigin = freezed,
    Object? trailer = freezed,
    Object? image = freezed,
    Object? popularity = freezed,
    Object? color = freezed,
    Object? cover = freezed,
    Object? description = freezed,
    Object? status = freezed,
    Object? releaseDate = freezed,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? totalEpisodes = freezed,
    Object? rating = freezed,
    Object? duration = freezed,
    Object? genres = freezed,
    Object? season = freezed,
    Object? studios = freezed,
    Object? subOrDub = freezed,
    Object? type = freezed,
    Object? recommendations = freezed,
    Object? characters = freezed,
    Object? relations = freezed,
    Object? episodes = freezed,
  }) {
    return _then(_$AnimeInfoModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      synonyms: freezed == synonyms
          ? _value._synonyms
          : synonyms // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      isLicensed: freezed == isLicensed
          ? _value.isLicensed
          : isLicensed // ignore: cast_nullable_to_non_nullable
              as bool?,
      isAdult: freezed == isAdult
          ? _value.isAdult
          : isAdult // ignore: cast_nullable_to_non_nullable
              as bool?,
      countryOfOrigin: freezed == countryOfOrigin
          ? _value.countryOfOrigin
          : countryOfOrigin // ignore: cast_nullable_to_non_nullable
              as String?,
      trailer: freezed == trailer
          ? _value.trailer
          : trailer // ignore: cast_nullable_to_non_nullable
              as AnimeTrailer?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      popularity: freezed == popularity
          ? _value.popularity
          : popularity // ignore: cast_nullable_to_non_nullable
              as int?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      releaseDate: freezed == releaseDate
          ? _value.releaseDate
          : releaseDate // ignore: cast_nullable_to_non_nullable
              as int?,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as StartDate?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as StartDate?,
      totalEpisodes: freezed == totalEpisodes
          ? _value.totalEpisodes
          : totalEpisodes // ignore: cast_nullable_to_non_nullable
              as int?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
      duration: freezed == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int?,
      genres: freezed == genres
          ? _value._genres
          : genres // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      season: freezed == season
          ? _value.season
          : season // ignore: cast_nullable_to_non_nullable
              as String?,
      studios: freezed == studios
          ? _value._studios
          : studios // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      subOrDub: freezed == subOrDub
          ? _value.subOrDub
          : subOrDub // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      recommendations: freezed == recommendations
          ? _value._recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<Recommendation>?,
      characters: freezed == characters
          ? _value._characters
          : characters // ignore: cast_nullable_to_non_nullable
              as List<Character>?,
      relations: freezed == relations
          ? _value._relations
          : relations // ignore: cast_nullable_to_non_nullable
              as List<Relation>?,
      episodes: freezed == episodes
          ? _value._episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as List<Episode>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AnimeInfoModelImpl implements _AnimeInfoModel {
  const _$AnimeInfoModelImpl(
      {this.id,
      this.title,
      this.malId,
      final List<String>? synonyms,
      this.isLicensed,
      this.isAdult,
      this.countryOfOrigin,
      this.trailer,
      this.image,
      this.popularity,
      this.color,
      this.cover,
      this.description,
      this.status,
      this.releaseDate,
      this.startDate,
      this.endDate,
      this.totalEpisodes,
      this.rating,
      this.duration,
      final List<String>? genres,
      this.season,
      final List<String>? studios,
      this.subOrDub,
      this.type,
      final List<Recommendation>? recommendations,
      final List<Character>? characters,
      final List<Relation>? relations,
      final List<Episode>? episodes})
      : _synonyms = synonyms,
        _genres = genres,
        _studios = studios,
        _recommendations = recommendations,
        _characters = characters,
        _relations = relations,
        _episodes = episodes;

  factory _$AnimeInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AnimeInfoModelImplFromJson(json);

  @override
  final String? id;
  @override
  final AnimeTitle? title;
  @override
  final int? malId;
  final List<String>? _synonyms;
  @override
  List<String>? get synonyms {
    final value = _synonyms;
    if (value == null) return null;
    if (_synonyms is EqualUnmodifiableListView) return _synonyms;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final bool? isLicensed;
  @override
  final bool? isAdult;
  @override
  final String? countryOfOrigin;
  @override
  final AnimeTrailer? trailer;
  @override
  final String? image;
  @override
  final int? popularity;
  @override
  final String? color;
  @override
  final String? cover;
  @override
  final String? description;
  @override
  final String? status;
  @override
  final int? releaseDate;
  @override
  final StartDate? startDate;
  @override
  final StartDate? endDate;
  @override
  final int? totalEpisodes;
  @override
  final int? rating;
  @override
  final int? duration;
  final List<String>? _genres;
  @override
  List<String>? get genres {
    final value = _genres;
    if (value == null) return null;
    if (_genres is EqualUnmodifiableListView) return _genres;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? season;
  final List<String>? _studios;
  @override
  List<String>? get studios {
    final value = _studios;
    if (value == null) return null;
    if (_studios is EqualUnmodifiableListView) return _studios;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? subOrDub;
  @override
  final String? type;
  final List<Recommendation>? _recommendations;
  @override
  List<Recommendation>? get recommendations {
    final value = _recommendations;
    if (value == null) return null;
    if (_recommendations is EqualUnmodifiableListView) return _recommendations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Character>? _characters;
  @override
  List<Character>? get characters {
    final value = _characters;
    if (value == null) return null;
    if (_characters is EqualUnmodifiableListView) return _characters;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Relation>? _relations;
  @override
  List<Relation>? get relations {
    final value = _relations;
    if (value == null) return null;
    if (_relations is EqualUnmodifiableListView) return _relations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Episode>? _episodes;
  @override
  List<Episode>? get episodes {
    final value = _episodes;
    if (value == null) return null;
    if (_episodes is EqualUnmodifiableListView) return _episodes;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'AnimeInfoModel(id: $id, title: $title, malId: $malId, synonyms: $synonyms, isLicensed: $isLicensed, isAdult: $isAdult, countryOfOrigin: $countryOfOrigin, trailer: $trailer, image: $image, popularity: $popularity, color: $color, cover: $cover, description: $description, status: $status, releaseDate: $releaseDate, startDate: $startDate, endDate: $endDate, totalEpisodes: $totalEpisodes, rating: $rating, duration: $duration, genres: $genres, season: $season, studios: $studios, subOrDub: $subOrDub, type: $type, recommendations: $recommendations, characters: $characters, relations: $relations, episodes: $episodes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AnimeInfoModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.malId, malId) || other.malId == malId) &&
            const DeepCollectionEquality().equals(other._synonyms, _synonyms) &&
            (identical(other.isLicensed, isLicensed) ||
                other.isLicensed == isLicensed) &&
            (identical(other.isAdult, isAdult) || other.isAdult == isAdult) &&
            (identical(other.countryOfOrigin, countryOfOrigin) ||
                other.countryOfOrigin == countryOfOrigin) &&
            (identical(other.trailer, trailer) || other.trailer == trailer) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.popularity, popularity) ||
                other.popularity == popularity) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.releaseDate, releaseDate) ||
                other.releaseDate == releaseDate) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.totalEpisodes, totalEpisodes) ||
                other.totalEpisodes == totalEpisodes) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            const DeepCollectionEquality().equals(other._genres, _genres) &&
            (identical(other.season, season) || other.season == season) &&
            const DeepCollectionEquality().equals(other._studios, _studios) &&
            (identical(other.subOrDub, subOrDub) ||
                other.subOrDub == subOrDub) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other._recommendations, _recommendations) &&
            const DeepCollectionEquality()
                .equals(other._characters, _characters) &&
            const DeepCollectionEquality()
                .equals(other._relations, _relations) &&
            const DeepCollectionEquality().equals(other._episodes, _episodes));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        title,
        malId,
        const DeepCollectionEquality().hash(_synonyms),
        isLicensed,
        isAdult,
        countryOfOrigin,
        trailer,
        image,
        popularity,
        color,
        cover,
        description,
        status,
        releaseDate,
        startDate,
        endDate,
        totalEpisodes,
        rating,
        duration,
        const DeepCollectionEquality().hash(_genres),
        season,
        const DeepCollectionEquality().hash(_studios),
        subOrDub,
        type,
        const DeepCollectionEquality().hash(_recommendations),
        const DeepCollectionEquality().hash(_characters),
        const DeepCollectionEquality().hash(_relations),
        const DeepCollectionEquality().hash(_episodes)
      ]);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AnimeInfoModelImplCopyWith<_$AnimeInfoModelImpl> get copyWith =>
      __$$AnimeInfoModelImplCopyWithImpl<_$AnimeInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AnimeInfoModelImplToJson(
      this,
    );
  }
}

abstract class _AnimeInfoModel implements AnimeInfoModel {
  const factory _AnimeInfoModel(
      {final String? id,
      final AnimeTitle? title,
      final int? malId,
      final List<String>? synonyms,
      final bool? isLicensed,
      final bool? isAdult,
      final String? countryOfOrigin,
      final AnimeTrailer? trailer,
      final String? image,
      final int? popularity,
      final String? color,
      final String? cover,
      final String? description,
      final String? status,
      final int? releaseDate,
      final StartDate? startDate,
      final StartDate? endDate,
      final int? totalEpisodes,
      final int? rating,
      final int? duration,
      final List<String>? genres,
      final String? season,
      final List<String>? studios,
      final String? subOrDub,
      final String? type,
      final List<Recommendation>? recommendations,
      final List<Character>? characters,
      final List<Relation>? relations,
      final List<Episode>? episodes}) = _$AnimeInfoModelImpl;

  factory _AnimeInfoModel.fromJson(Map<String, dynamic> json) =
      _$AnimeInfoModelImpl.fromJson;

  @override
  String? get id;
  @override
  AnimeTitle? get title;
  @override
  int? get malId;
  @override
  List<String>? get synonyms;
  @override
  bool? get isLicensed;
  @override
  bool? get isAdult;
  @override
  String? get countryOfOrigin;
  @override
  AnimeTrailer? get trailer;
  @override
  String? get image;
  @override
  int? get popularity;
  @override
  String? get color;
  @override
  String? get cover;
  @override
  String? get description;
  @override
  String? get status;
  @override
  int? get releaseDate;
  @override
  StartDate? get startDate;
  @override
  StartDate? get endDate;
  @override
  int? get totalEpisodes;
  @override
  int? get rating;
  @override
  int? get duration;
  @override
  List<String>? get genres;
  @override
  String? get season;
  @override
  List<String>? get studios;
  @override
  String? get subOrDub;
  @override
  String? get type;
  @override
  List<Recommendation>? get recommendations;
  @override
  List<Character>? get characters;
  @override
  List<Relation>? get relations;
  @override
  List<Episode>? get episodes;
  @override
  @JsonKey(ignore: true)
  _$$AnimeInfoModelImplCopyWith<_$AnimeInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StartDate _$StartDateFromJson(Map<String, dynamic> json) {
  return _StartDate.fromJson(json);
}

/// @nodoc
mixin _$StartDate {
  int? get year => throw _privateConstructorUsedError;
  int? get month => throw _privateConstructorUsedError;
  int? get day => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $StartDateCopyWith<StartDate> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StartDateCopyWith<$Res> {
  factory $StartDateCopyWith(StartDate value, $Res Function(StartDate) then) =
      _$StartDateCopyWithImpl<$Res, StartDate>;
  @useResult
  $Res call({int? year, int? month, int? day});
}

/// @nodoc
class _$StartDateCopyWithImpl<$Res, $Val extends StartDate>
    implements $StartDateCopyWith<$Res> {
  _$StartDateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? month = freezed,
    Object? day = freezed,
  }) {
    return _then(_value.copyWith(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int?,
      day: freezed == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StartDateImplCopyWith<$Res>
    implements $StartDateCopyWith<$Res> {
  factory _$$StartDateImplCopyWith(
          _$StartDateImpl value, $Res Function(_$StartDateImpl) then) =
      __$$StartDateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? year, int? month, int? day});
}

/// @nodoc
class __$$StartDateImplCopyWithImpl<$Res>
    extends _$StartDateCopyWithImpl<$Res, _$StartDateImpl>
    implements _$$StartDateImplCopyWith<$Res> {
  __$$StartDateImplCopyWithImpl(
      _$StartDateImpl _value, $Res Function(_$StartDateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = freezed,
    Object? month = freezed,
    Object? day = freezed,
  }) {
    return _then(_$StartDateImpl(
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int?,
      month: freezed == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int?,
      day: freezed == day
          ? _value.day
          : day // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StartDateImpl implements _StartDate {
  const _$StartDateImpl({this.year, this.month, this.day});

  factory _$StartDateImpl.fromJson(Map<String, dynamic> json) =>
      _$$StartDateImplFromJson(json);

  @override
  final int? year;
  @override
  final int? month;
  @override
  final int? day;

  @override
  String toString() {
    return 'StartDate(year: $year, month: $month, day: $day)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StartDateImpl &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.month, month) || other.month == month) &&
            (identical(other.day, day) || other.day == day));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, year, month, day);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$StartDateImplCopyWith<_$StartDateImpl> get copyWith =>
      __$$StartDateImplCopyWithImpl<_$StartDateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StartDateImplToJson(
      this,
    );
  }
}

abstract class _StartDate implements StartDate {
  const factory _StartDate(
      {final int? year, final int? month, final int? day}) = _$StartDateImpl;

  factory _StartDate.fromJson(Map<String, dynamic> json) =
      _$StartDateImpl.fromJson;

  @override
  int? get year;
  @override
  int? get month;
  @override
  int? get day;
  @override
  @JsonKey(ignore: true)
  _$$StartDateImplCopyWith<_$StartDateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Recommendation _$RecommendationFromJson(Map<String, dynamic> json) {
  return _Recommendation.fromJson(json);
}

/// @nodoc
mixin _$Recommendation {
  int? get id => throw _privateConstructorUsedError;
  int? get malId => throw _privateConstructorUsedError;
  AnimeTitle? get title => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  int? get episodes => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get cover => throw _privateConstructorUsedError;
  int? get rating => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RecommendationCopyWith<Recommendation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecommendationCopyWith<$Res> {
  factory $RecommendationCopyWith(
          Recommendation value, $Res Function(Recommendation) then) =
      _$RecommendationCopyWithImpl<$Res, Recommendation>;
  @useResult
  $Res call(
      {int? id,
      int? malId,
      AnimeTitle? title,
      String? status,
      int? episodes,
      String? image,
      String? cover,
      int? rating,
      String? type});

  $AnimeTitleCopyWith<$Res>? get title;
}

/// @nodoc
class _$RecommendationCopyWithImpl<$Res, $Val extends Recommendation>
    implements $RecommendationCopyWith<$Res> {
  _$RecommendationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? malId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? episodes = freezed,
    Object? image = freezed,
    Object? cover = freezed,
    Object? rating = freezed,
    Object? type = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      episodes: freezed == episodes
          ? _value.episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AnimeTitleCopyWith<$Res>? get title {
    if (_value.title == null) {
      return null;
    }

    return $AnimeTitleCopyWith<$Res>(_value.title!, (value) {
      return _then(_value.copyWith(title: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RecommendationImplCopyWith<$Res>
    implements $RecommendationCopyWith<$Res> {
  factory _$$RecommendationImplCopyWith(_$RecommendationImpl value,
          $Res Function(_$RecommendationImpl) then) =
      __$$RecommendationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? malId,
      AnimeTitle? title,
      String? status,
      int? episodes,
      String? image,
      String? cover,
      int? rating,
      String? type});

  @override
  $AnimeTitleCopyWith<$Res>? get title;
}

/// @nodoc
class __$$RecommendationImplCopyWithImpl<$Res>
    extends _$RecommendationCopyWithImpl<$Res, _$RecommendationImpl>
    implements _$$RecommendationImplCopyWith<$Res> {
  __$$RecommendationImplCopyWithImpl(
      _$RecommendationImpl _value, $Res Function(_$RecommendationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? malId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? episodes = freezed,
    Object? image = freezed,
    Object? cover = freezed,
    Object? rating = freezed,
    Object? type = freezed,
  }) {
    return _then(_$RecommendationImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      episodes: freezed == episodes
          ? _value.episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecommendationImpl implements _Recommendation {
  const _$RecommendationImpl(
      {this.id,
      this.malId,
      this.title,
      this.status,
      this.episodes,
      this.image,
      this.cover,
      this.rating,
      this.type});

  factory _$RecommendationImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecommendationImplFromJson(json);

  @override
  final int? id;
  @override
  final int? malId;
  @override
  final AnimeTitle? title;
  @override
  final String? status;
  @override
  final int? episodes;
  @override
  final String? image;
  @override
  final String? cover;
  @override
  final int? rating;
  @override
  final String? type;

  @override
  String toString() {
    return 'Recommendation(id: $id, malId: $malId, title: $title, status: $status, episodes: $episodes, image: $image, cover: $cover, rating: $rating, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecommendationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.malId, malId) || other.malId == malId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.episodes, episodes) ||
                other.episodes == episodes) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            (identical(other.type, type) || other.type == type));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, malId, title, status,
      episodes, image, cover, rating, type);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RecommendationImplCopyWith<_$RecommendationImpl> get copyWith =>
      __$$RecommendationImplCopyWithImpl<_$RecommendationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecommendationImplToJson(
      this,
    );
  }
}

abstract class _Recommendation implements Recommendation {
  const factory _Recommendation(
      {final int? id,
      final int? malId,
      final AnimeTitle? title,
      final String? status,
      final int? episodes,
      final String? image,
      final String? cover,
      final int? rating,
      final String? type}) = _$RecommendationImpl;

  factory _Recommendation.fromJson(Map<String, dynamic> json) =
      _$RecommendationImpl.fromJson;

  @override
  int? get id;
  @override
  int? get malId;
  @override
  AnimeTitle? get title;
  @override
  String? get status;
  @override
  int? get episodes;
  @override
  String? get image;
  @override
  String? get cover;
  @override
  int? get rating;
  @override
  String? get type;
  @override
  @JsonKey(ignore: true)
  _$$RecommendationImplCopyWith<_$RecommendationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Character _$CharacterFromJson(Map<String, dynamic> json) {
  return _Character.fromJson(json);
}

/// @nodoc
mixin _$Character {
  int? get id => throw _privateConstructorUsedError;
  String? get role => throw _privateConstructorUsedError;
  VAname? get name => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  List<VoiceActors>? get voiceActors => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $CharacterCopyWith<Character> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CharacterCopyWith<$Res> {
  factory $CharacterCopyWith(Character value, $Res Function(Character) then) =
      _$CharacterCopyWithImpl<$Res, Character>;
  @useResult
  $Res call(
      {int? id,
      String? role,
      VAname? name,
      String? image,
      List<VoiceActors>? voiceActors});

  $VAnameCopyWith<$Res>? get name;
}

/// @nodoc
class _$CharacterCopyWithImpl<$Res, $Val extends Character>
    implements $CharacterCopyWith<$Res> {
  _$CharacterCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? role = freezed,
    Object? name = freezed,
    Object? image = freezed,
    Object? voiceActors = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as VAname?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceActors: freezed == voiceActors
          ? _value.voiceActors
          : voiceActors // ignore: cast_nullable_to_non_nullable
              as List<VoiceActors>?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VAnameCopyWith<$Res>? get name {
    if (_value.name == null) {
      return null;
    }

    return $VAnameCopyWith<$Res>(_value.name!, (value) {
      return _then(_value.copyWith(name: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$CharacterImplCopyWith<$Res>
    implements $CharacterCopyWith<$Res> {
  factory _$$CharacterImplCopyWith(
          _$CharacterImpl value, $Res Function(_$CharacterImpl) then) =
      __$$CharacterImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? role,
      VAname? name,
      String? image,
      List<VoiceActors>? voiceActors});

  @override
  $VAnameCopyWith<$Res>? get name;
}

/// @nodoc
class __$$CharacterImplCopyWithImpl<$Res>
    extends _$CharacterCopyWithImpl<$Res, _$CharacterImpl>
    implements _$$CharacterImplCopyWith<$Res> {
  __$$CharacterImplCopyWithImpl(
      _$CharacterImpl _value, $Res Function(_$CharacterImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? role = freezed,
    Object? name = freezed,
    Object? image = freezed,
    Object? voiceActors = freezed,
  }) {
    return _then(_$CharacterImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      role: freezed == role
          ? _value.role
          : role // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as VAname?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      voiceActors: freezed == voiceActors
          ? _value._voiceActors
          : voiceActors // ignore: cast_nullable_to_non_nullable
              as List<VoiceActors>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CharacterImpl implements _Character {
  const _$CharacterImpl(
      {this.id,
      this.role,
      this.name,
      this.image,
      final List<VoiceActors>? voiceActors})
      : _voiceActors = voiceActors;

  factory _$CharacterImpl.fromJson(Map<String, dynamic> json) =>
      _$$CharacterImplFromJson(json);

  @override
  final int? id;
  @override
  final String? role;
  @override
  final VAname? name;
  @override
  final String? image;
  final List<VoiceActors>? _voiceActors;
  @override
  List<VoiceActors>? get voiceActors {
    final value = _voiceActors;
    if (value == null) return null;
    if (_voiceActors is EqualUnmodifiableListView) return _voiceActors;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'Character(id: $id, role: $role, name: $name, image: $image, voiceActors: $voiceActors)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CharacterImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.role, role) || other.role == role) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.image, image) || other.image == image) &&
            const DeepCollectionEquality()
                .equals(other._voiceActors, _voiceActors));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, role, name, image,
      const DeepCollectionEquality().hash(_voiceActors));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CharacterImplCopyWith<_$CharacterImpl> get copyWith =>
      __$$CharacterImplCopyWithImpl<_$CharacterImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CharacterImplToJson(
      this,
    );
  }
}

abstract class _Character implements Character {
  const factory _Character(
      {final int? id,
      final String? role,
      final VAname? name,
      final String? image,
      final List<VoiceActors>? voiceActors}) = _$CharacterImpl;

  factory _Character.fromJson(Map<String, dynamic> json) =
      _$CharacterImpl.fromJson;

  @override
  int? get id;
  @override
  String? get role;
  @override
  VAname? get name;
  @override
  String? get image;
  @override
  List<VoiceActors>? get voiceActors;
  @override
  @JsonKey(ignore: true)
  _$$CharacterImplCopyWith<_$CharacterImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VoiceActors _$VoiceActorsFromJson(Map<String, dynamic> json) {
  return _VoiceActors.fromJson(json);
}

/// @nodoc
mixin _$VoiceActors {
  int? get id => throw _privateConstructorUsedError;
  VAname? get name => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VoiceActorsCopyWith<VoiceActors> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VoiceActorsCopyWith<$Res> {
  factory $VoiceActorsCopyWith(
          VoiceActors value, $Res Function(VoiceActors) then) =
      _$VoiceActorsCopyWithImpl<$Res, VoiceActors>;
  @useResult
  $Res call({int? id, VAname? name, String? image});

  $VAnameCopyWith<$Res>? get name;
}

/// @nodoc
class _$VoiceActorsCopyWithImpl<$Res, $Val extends VoiceActors>
    implements $VoiceActorsCopyWith<$Res> {
  _$VoiceActorsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? image = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as VAname?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $VAnameCopyWith<$Res>? get name {
    if (_value.name == null) {
      return null;
    }

    return $VAnameCopyWith<$Res>(_value.name!, (value) {
      return _then(_value.copyWith(name: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$VoiceActorsImplCopyWith<$Res>
    implements $VoiceActorsCopyWith<$Res> {
  factory _$$VoiceActorsImplCopyWith(
          _$VoiceActorsImpl value, $Res Function(_$VoiceActorsImpl) then) =
      __$$VoiceActorsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, VAname? name, String? image});

  @override
  $VAnameCopyWith<$Res>? get name;
}

/// @nodoc
class __$$VoiceActorsImplCopyWithImpl<$Res>
    extends _$VoiceActorsCopyWithImpl<$Res, _$VoiceActorsImpl>
    implements _$$VoiceActorsImplCopyWith<$Res> {
  __$$VoiceActorsImplCopyWithImpl(
      _$VoiceActorsImpl _value, $Res Function(_$VoiceActorsImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? image = freezed,
  }) {
    return _then(_$VoiceActorsImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as VAname?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VoiceActorsImpl implements _VoiceActors {
  const _$VoiceActorsImpl({this.id, this.name, this.image});

  factory _$VoiceActorsImpl.fromJson(Map<String, dynamic> json) =>
      _$$VoiceActorsImplFromJson(json);

  @override
  final int? id;
  @override
  final VAname? name;
  @override
  final String? image;

  @override
  String toString() {
    return 'VoiceActors(id: $id, name: $name, image: $image)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VoiceActorsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.image, image) || other.image == image));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, image);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VoiceActorsImplCopyWith<_$VoiceActorsImpl> get copyWith =>
      __$$VoiceActorsImplCopyWithImpl<_$VoiceActorsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VoiceActorsImplToJson(
      this,
    );
  }
}

abstract class _VoiceActors implements VoiceActors {
  const factory _VoiceActors(
      {final int? id,
      final VAname? name,
      final String? image}) = _$VoiceActorsImpl;

  factory _VoiceActors.fromJson(Map<String, dynamic> json) =
      _$VoiceActorsImpl.fromJson;

  @override
  int? get id;
  @override
  VAname? get name;
  @override
  String? get image;
  @override
  @JsonKey(ignore: true)
  _$$VoiceActorsImplCopyWith<_$VoiceActorsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VAname _$VAnameFromJson(Map<String, dynamic> json) {
  return _VAname.fromJson(json);
}

/// @nodoc
mixin _$VAname {
  String? get first => throw _privateConstructorUsedError;
  String? get last => throw _privateConstructorUsedError;
  String? get full => throw _privateConstructorUsedError;
  String? get native => throw _privateConstructorUsedError;
  String? get userPreferred => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $VAnameCopyWith<VAname> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VAnameCopyWith<$Res> {
  factory $VAnameCopyWith(VAname value, $Res Function(VAname) then) =
      _$VAnameCopyWithImpl<$Res, VAname>;
  @useResult
  $Res call(
      {String? first,
      String? last,
      String? full,
      String? native,
      String? userPreferred});
}

/// @nodoc
class _$VAnameCopyWithImpl<$Res, $Val extends VAname>
    implements $VAnameCopyWith<$Res> {
  _$VAnameCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? first = freezed,
    Object? last = freezed,
    Object? full = freezed,
    Object? native = freezed,
    Object? userPreferred = freezed,
  }) {
    return _then(_value.copyWith(
      first: freezed == first
          ? _value.first
          : first // ignore: cast_nullable_to_non_nullable
              as String?,
      last: freezed == last
          ? _value.last
          : last // ignore: cast_nullable_to_non_nullable
              as String?,
      full: freezed == full
          ? _value.full
          : full // ignore: cast_nullable_to_non_nullable
              as String?,
      native: freezed == native
          ? _value.native
          : native // ignore: cast_nullable_to_non_nullable
              as String?,
      userPreferred: freezed == userPreferred
          ? _value.userPreferred
          : userPreferred // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VAnameImplCopyWith<$Res> implements $VAnameCopyWith<$Res> {
  factory _$$VAnameImplCopyWith(
          _$VAnameImpl value, $Res Function(_$VAnameImpl) then) =
      __$$VAnameImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? first,
      String? last,
      String? full,
      String? native,
      String? userPreferred});
}

/// @nodoc
class __$$VAnameImplCopyWithImpl<$Res>
    extends _$VAnameCopyWithImpl<$Res, _$VAnameImpl>
    implements _$$VAnameImplCopyWith<$Res> {
  __$$VAnameImplCopyWithImpl(
      _$VAnameImpl _value, $Res Function(_$VAnameImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? first = freezed,
    Object? last = freezed,
    Object? full = freezed,
    Object? native = freezed,
    Object? userPreferred = freezed,
  }) {
    return _then(_$VAnameImpl(
      first: freezed == first
          ? _value.first
          : first // ignore: cast_nullable_to_non_nullable
              as String?,
      last: freezed == last
          ? _value.last
          : last // ignore: cast_nullable_to_non_nullable
              as String?,
      full: freezed == full
          ? _value.full
          : full // ignore: cast_nullable_to_non_nullable
              as String?,
      native: freezed == native
          ? _value.native
          : native // ignore: cast_nullable_to_non_nullable
              as String?,
      userPreferred: freezed == userPreferred
          ? _value.userPreferred
          : userPreferred // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VAnameImpl implements _VAname {
  const _$VAnameImpl(
      {this.first, this.last, this.full, this.native, this.userPreferred});

  factory _$VAnameImpl.fromJson(Map<String, dynamic> json) =>
      _$$VAnameImplFromJson(json);

  @override
  final String? first;
  @override
  final String? last;
  @override
  final String? full;
  @override
  final String? native;
  @override
  final String? userPreferred;

  @override
  String toString() {
    return 'VAname(first: $first, last: $last, full: $full, native: $native, userPreferred: $userPreferred)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VAnameImpl &&
            (identical(other.first, first) || other.first == first) &&
            (identical(other.last, last) || other.last == last) &&
            (identical(other.full, full) || other.full == full) &&
            (identical(other.native, native) || other.native == native) &&
            (identical(other.userPreferred, userPreferred) ||
                other.userPreferred == userPreferred));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, first, last, full, native, userPreferred);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$VAnameImplCopyWith<_$VAnameImpl> get copyWith =>
      __$$VAnameImplCopyWithImpl<_$VAnameImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VAnameImplToJson(
      this,
    );
  }
}

abstract class _VAname implements VAname {
  const factory _VAname(
      {final String? first,
      final String? last,
      final String? full,
      final String? native,
      final String? userPreferred}) = _$VAnameImpl;

  factory _VAname.fromJson(Map<String, dynamic> json) = _$VAnameImpl.fromJson;

  @override
  String? get first;
  @override
  String? get last;
  @override
  String? get full;
  @override
  String? get native;
  @override
  String? get userPreferred;
  @override
  @JsonKey(ignore: true)
  _$$VAnameImplCopyWith<_$VAnameImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Relation _$RelationFromJson(Map<String, dynamic> json) {
  return _Relation.fromJson(json);
}

/// @nodoc
mixin _$Relation {
  int? get id => throw _privateConstructorUsedError;
  String? get relationType => throw _privateConstructorUsedError;
  int? get malId => throw _privateConstructorUsedError;
  AnimeTitle? get title => throw _privateConstructorUsedError;
  String? get status => throw _privateConstructorUsedError;
  int? get episodes => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get color => throw _privateConstructorUsedError;
  String? get type => throw _privateConstructorUsedError;
  String? get cover => throw _privateConstructorUsedError;
  int? get rating => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $RelationCopyWith<Relation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RelationCopyWith<$Res> {
  factory $RelationCopyWith(Relation value, $Res Function(Relation) then) =
      _$RelationCopyWithImpl<$Res, Relation>;
  @useResult
  $Res call(
      {int? id,
      String? relationType,
      int? malId,
      AnimeTitle? title,
      String? status,
      int? episodes,
      String? image,
      String? color,
      String? type,
      String? cover,
      int? rating});

  $AnimeTitleCopyWith<$Res>? get title;
}

/// @nodoc
class _$RelationCopyWithImpl<$Res, $Val extends Relation>
    implements $RelationCopyWith<$Res> {
  _$RelationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? relationType = freezed,
    Object? malId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? episodes = freezed,
    Object? image = freezed,
    Object? color = freezed,
    Object? type = freezed,
    Object? cover = freezed,
    Object? rating = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      relationType: freezed == relationType
          ? _value.relationType
          : relationType // ignore: cast_nullable_to_non_nullable
              as String?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      episodes: freezed == episodes
          ? _value.episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $AnimeTitleCopyWith<$Res>? get title {
    if (_value.title == null) {
      return null;
    }

    return $AnimeTitleCopyWith<$Res>(_value.title!, (value) {
      return _then(_value.copyWith(title: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$RelationImplCopyWith<$Res>
    implements $RelationCopyWith<$Res> {
  factory _$$RelationImplCopyWith(
          _$RelationImpl value, $Res Function(_$RelationImpl) then) =
      __$$RelationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? relationType,
      int? malId,
      AnimeTitle? title,
      String? status,
      int? episodes,
      String? image,
      String? color,
      String? type,
      String? cover,
      int? rating});

  @override
  $AnimeTitleCopyWith<$Res>? get title;
}

/// @nodoc
class __$$RelationImplCopyWithImpl<$Res>
    extends _$RelationCopyWithImpl<$Res, _$RelationImpl>
    implements _$$RelationImplCopyWith<$Res> {
  __$$RelationImplCopyWithImpl(
      _$RelationImpl _value, $Res Function(_$RelationImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? relationType = freezed,
    Object? malId = freezed,
    Object? title = freezed,
    Object? status = freezed,
    Object? episodes = freezed,
    Object? image = freezed,
    Object? color = freezed,
    Object? type = freezed,
    Object? cover = freezed,
    Object? rating = freezed,
  }) {
    return _then(_$RelationImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      relationType: freezed == relationType
          ? _value.relationType
          : relationType // ignore: cast_nullable_to_non_nullable
              as String?,
      malId: freezed == malId
          ? _value.malId
          : malId // ignore: cast_nullable_to_non_nullable
              as int?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as AnimeTitle?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String?,
      episodes: freezed == episodes
          ? _value.episodes
          : episodes // ignore: cast_nullable_to_non_nullable
              as int?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      color: freezed == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      cover: freezed == cover
          ? _value.cover
          : cover // ignore: cast_nullable_to_non_nullable
              as String?,
      rating: freezed == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RelationImpl implements _Relation {
  const _$RelationImpl(
      {this.id,
      this.relationType,
      this.malId,
      this.title,
      this.status,
      this.episodes,
      this.image,
      this.color,
      this.type,
      this.cover,
      this.rating});

  factory _$RelationImpl.fromJson(Map<String, dynamic> json) =>
      _$$RelationImplFromJson(json);

  @override
  final int? id;
  @override
  final String? relationType;
  @override
  final int? malId;
  @override
  final AnimeTitle? title;
  @override
  final String? status;
  @override
  final int? episodes;
  @override
  final String? image;
  @override
  final String? color;
  @override
  final String? type;
  @override
  final String? cover;
  @override
  final int? rating;

  @override
  String toString() {
    return 'Relation(id: $id, relationType: $relationType, malId: $malId, title: $title, status: $status, episodes: $episodes, image: $image, color: $color, type: $type, cover: $cover, rating: $rating)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RelationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.relationType, relationType) ||
                other.relationType == relationType) &&
            (identical(other.malId, malId) || other.malId == malId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.episodes, episodes) ||
                other.episodes == episodes) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.cover, cover) || other.cover == cover) &&
            (identical(other.rating, rating) || other.rating == rating));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, relationType, malId, title,
      status, episodes, image, color, type, cover, rating);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$RelationImplCopyWith<_$RelationImpl> get copyWith =>
      __$$RelationImplCopyWithImpl<_$RelationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RelationImplToJson(
      this,
    );
  }
}

abstract class _Relation implements Relation {
  const factory _Relation(
      {final int? id,
      final String? relationType,
      final int? malId,
      final AnimeTitle? title,
      final String? status,
      final int? episodes,
      final String? image,
      final String? color,
      final String? type,
      final String? cover,
      final int? rating}) = _$RelationImpl;

  factory _Relation.fromJson(Map<String, dynamic> json) =
      _$RelationImpl.fromJson;

  @override
  int? get id;
  @override
  String? get relationType;
  @override
  int? get malId;
  @override
  AnimeTitle? get title;
  @override
  String? get status;
  @override
  int? get episodes;
  @override
  String? get image;
  @override
  String? get color;
  @override
  String? get type;
  @override
  String? get cover;
  @override
  int? get rating;
  @override
  @JsonKey(ignore: true)
  _$$RelationImplCopyWith<_$RelationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Episode _$EpisodeFromJson(Map<String, dynamic> json) {
  return _Episode.fromJson(json);
}

/// @nodoc
mixin _$Episode {
  String? get id => throw _privateConstructorUsedError;
  String? get title => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  int? get number => throw _privateConstructorUsedError;
  String? get description => throw _privateConstructorUsedError;
  String? get url => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $EpisodeCopyWith<Episode> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $EpisodeCopyWith<$Res> {
  factory $EpisodeCopyWith(Episode value, $Res Function(Episode) then) =
      _$EpisodeCopyWithImpl<$Res, Episode>;
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? image,
      int? number,
      String? description,
      String? url});
}

/// @nodoc
class _$EpisodeCopyWithImpl<$Res, $Val extends Episode>
    implements $EpisodeCopyWith<$Res> {
  _$EpisodeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? image = freezed,
    Object? number = freezed,
    Object? description = freezed,
    Object? url = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$EpisodeImplCopyWith<$Res> implements $EpisodeCopyWith<$Res> {
  factory _$$EpisodeImplCopyWith(
          _$EpisodeImpl value, $Res Function(_$EpisodeImpl) then) =
      __$$EpisodeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? id,
      String? title,
      String? image,
      int? number,
      String? description,
      String? url});
}

/// @nodoc
class __$$EpisodeImplCopyWithImpl<$Res>
    extends _$EpisodeCopyWithImpl<$Res, _$EpisodeImpl>
    implements _$$EpisodeImplCopyWith<$Res> {
  __$$EpisodeImplCopyWithImpl(
      _$EpisodeImpl _value, $Res Function(_$EpisodeImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? title = freezed,
    Object? image = freezed,
    Object? number = freezed,
    Object? description = freezed,
    Object? url = freezed,
  }) {
    return _then(_$EpisodeImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$EpisodeImpl implements _Episode {
  const _$EpisodeImpl(
      {this.id,
      this.title,
      this.image,
      this.number,
      this.description,
      this.url});

  factory _$EpisodeImpl.fromJson(Map<String, dynamic> json) =>
      _$$EpisodeImplFromJson(json);

  @override
  final String? id;
  @override
  final String? title;
  @override
  final String? image;
  @override
  final int? number;
  @override
  final String? description;
  @override
  final String? url;

  @override
  String toString() {
    return 'Episode(id: $id, title: $title, image: $image, number: $number, description: $description, url: $url)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$EpisodeImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.number, number) || other.number == number) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.url, url) || other.url == url));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, title, image, number, description, url);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$EpisodeImplCopyWith<_$EpisodeImpl> get copyWith =>
      __$$EpisodeImplCopyWithImpl<_$EpisodeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$EpisodeImplToJson(
      this,
    );
  }
}

abstract class _Episode implements Episode {
  const factory _Episode(
      {final String? id,
      final String? title,
      final String? image,
      final int? number,
      final String? description,
      final String? url}) = _$EpisodeImpl;

  factory _Episode.fromJson(Map<String, dynamic> json) = _$EpisodeImpl.fromJson;

  @override
  String? get id;
  @override
  String? get title;
  @override
  String? get image;
  @override
  int? get number;
  @override
  String? get description;
  @override
  String? get url;
  @override
  @JsonKey(ignore: true)
  _$$EpisodeImplCopyWith<_$EpisodeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

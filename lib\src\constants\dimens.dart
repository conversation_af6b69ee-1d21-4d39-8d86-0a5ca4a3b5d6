import 'package:flutter/material.dart';

class AppDimens {
  AppDimens._();
  static const defPad4 = EdgeInsets.all(4);
  static const defPad = EdgeInsets.all(8);
  static const defPad16 = EdgeInsets.all(16);
  static const defPad22 = EdgeInsets.all(22);
  static const defPad24 = EdgeInsets.all(24);

  static const defPadV4 = EdgeInsets.symmetric(vertical: 4);
  static const defPadV = EdgeInsets.symmetric(vertical: 8);
  static const defPadV16 = EdgeInsets.symmetric(vertical: 16);
  static const defPadV24 = EdgeInsets.symmetric(vertical: 24);

  static const defPadH4 = EdgeInsets.symmetric(horizontal: 4);
  static const defPadH = EdgeInsets.symmetric(horizontal: 8);
  static const defPadH12 = EdgeInsets.symmetric(horizontal: 12);
  static const defPadH16 = EdgeInsets.symmetric(horizontal: 16);
  static const defPadH24 = EdgeInsets.symmetric(horizontal: 24);

  static const defRadius4 = 4.0;
  static const defRadius8 = 8.0;
  static const defRadius = 12.0;
  static const defRadius16 = 16.0;
  static const defRadius24 = 24.0;

  static const sizedBoxH5 = SizedBox(height: 5);
  static const sizedBoxH10 = SizedBox(height: 10);
  static const sizedBoxH15 = SizedBox(height: 15);
  static const sizedBoxH20 = SizedBox(height: 20);

  static const sizedBoxW5 = SizedBox(width: 5);
  static const sizedBoxW10 = SizedBox(width: 10);
  static const sizedBoxW15 = SizedBox(width: 15);
  static const sizedBoxW20 = SizedBox(width: 20);
}

import 'dart:developer';

import 'package:aniggo/src/data/data.dart';
import 'package:aniggo/src/domain/domain.dart';
import 'package:video_player/video_player.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class VideoPlayerNotifier extends StateNotifier<VideoPlayerState> {
  VideoPlayerNotifier({required this.episode, required this.animeInfoService})
      : super(const VideoPlayerState.loading("")) {
    init();
  }

  final Episode episode;
  final AnimeInfoData animeInfoService;

  VideoPlayerController? videoPlayerController;

  void init() async {
    state =
        const VideoPlayerState.loading("fetching episode urls from server...");
    try {
      log("init");
      if (episode.id == null) {
        state = const VideoPlayerState.error({
          "error": "Something went wrong, Please try with different provider"
        });
      }
      EpisodeUrl? episodeUrls = await getEpisodeUrl();
      if (episodeUrls != null) {
        initializePlayer(episodeUrls);
      }
    } catch (e, st) {
      log("$e");
      log("$st");
      state = VideoPlayerState.error(e);
    }
  }

  Future<EpisodeUrl?> getEpisodeUrl({bool cancelToken = false}) async {
    EpisodeUrl? episodeUrl;
    if (cancelToken) {
      final cancelToken = CancelToken();
      animeInfoService.getEpisodeUrl(
          episodeId: episode.id!, cancelToken: cancelToken);
    } else {
      episodeUrl = await animeInfoService.getEpisodeUrl(episodeId: episode.id!);
    }
    return episodeUrl;
  }

  Future<void> initializePlayer(EpisodeUrl episodeSource) async {
    state = const VideoPlayerState.loading("initialing player");
    List<Source>? sources = episodeSource.sources;
    Map<String, String>? headers = {
      if (episodeSource.headers != null) ...episodeSource.headers!,
      "user-agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:101.0) Gecko/20100101 Firefox/101.0"
    };

    /// if sources is empty return error
    if (sources == null) {
      state = const VideoPlayerState.error({"error": "No source found"});
      return;
    }

    String? firstSource() {
      String? url;
      try {
        var isAuto = sources
            .firstWhere(
                (element) =>
                    element.url != null && element.quality == "auto" ||
                    element.quality == "default",
                orElse: () =>
                    sources.firstWhere((element) => element.url != null))
            .url;
        if (isAuto != null) {
          url = isAuto.toString();
        } else {
          url = sources
              .firstWhere((element) => element.url != null)
              .url
              .toString();
        }
      } catch (e) {
        state = const VideoPlayerState.error({"error": "No source found"});
      }
      return url;
    }

    /// if [sources] is not null
    ///
    try {
      String? videoUrl = firstSource();
      if (videoUrl == null) {
        state =
            const VideoPlayerState.error({"error": "No valid source found"});
        return;
      }

      videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
        httpHeaders: headers,
      );

      await videoPlayerController!.initialize();
      await videoPlayerController!.play();

      state = VideoPlayerState.data(videoPlayerController!);
    } catch (e, st) {
      log("$e");
      log("$st");
      state = VideoPlayerState.error({
        "Something went wrong!\n Please try with different provider":
            e.toString()
      });
    }
  }

  @override
  void dispose() {
    getEpisodeUrl(cancelToken: true);
    videoPlayerController?.dispose();
    super.dispose();
  }
}

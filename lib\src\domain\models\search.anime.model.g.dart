// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search.anime.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SearchAnimeModelImpl _$$SearchAnimeModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SearchAnimeModelImpl(
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 0,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 0,
      totalResults: (json['totalResults'] as num?)?.toInt() ?? 0,
      results: (json['results'] as List<dynamic>?)
              ?.map((e) => SearchedResult.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$SearchAnimeModelImplToJson(
        _$SearchAnimeModelImpl instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'hasNextPage': instance.hasNextPage,
      'totalPages': instance.totalPages,
      'totalResults': instance.totalResults,
      'results': instance.results,
    };

_$SearchedResultImpl _$$SearchedResultImplFromJson(Map<String, dynamic> json) =>
    _$SearchedResultImpl(
      id: json['id'] as String?,
      malId: (json['malId'] as num?)?.toInt(),
      title: json['title'] == null
          ? null
          : AnimeTitle.fromJson(json['title'] as Map<String, dynamic>),
      status: json['status'] as String?,
      image: json['image'] as String?,
      cover: json['cover'] as String?,
      popularity: (json['popularity'] as num?)?.toInt(),
      totalEpisodes: (json['totalEpisodes'] as num?)?.toInt(),
      description: json['description'] as String?,
      genres:
          (json['genres'] as List<dynamic>?)?.map((e) => e as String).toList(),
      rating: (json['rating'] as num?)?.toInt(),
      color: json['color'] as String?,
      type: json['type'] as String?,
      releaseDate: (json['releaseDate'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SearchedResultImplToJson(
        _$SearchedResultImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'malId': instance.malId,
      'title': instance.title,
      'status': instance.status,
      'image': instance.image,
      'cover': instance.cover,
      'popularity': instance.popularity,
      'totalEpisodes': instance.totalEpisodes,
      'description': instance.description,
      'genres': instance.genres,
      'rating': instance.rating,
      'color': instance.color,
      'type': instance.type,
      'releaseDate': instance.releaseDate,
    };

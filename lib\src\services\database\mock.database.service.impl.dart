import 'package:aniggo/src/data/repositories/repositories.dart';
import 'package:aniggo/src/domain/domain.dart';

class MockDatabase implements DatabaseRepository {
  final List<Favourite> _favourites = [];

  @override
  Stream<List<Favourite>> getAnimes() {
    return Stream.value(_favourites);
  }

  @override
  Future<void> setAnime({required AnimeInfoModel anime}) async {
    // Mock implementation - do nothing on web
  }

  @override
  Future<Favourite?> getAnime(String id) async {
    return null;
  }

  @override
  Future<void> deleteAnime(String id) async {
    // Mock implementation - do nothing on web
  }

  @override
  Future<void> addEpisode({required String id, required int episodeNumber}) async {
    // Mock implementation - do nothing on web
  }

  @override
  Future<void> removeEpisode({required String id, required int episodeNumber}) async {
    // Mock implementation - do nothing on web
  }

  @override
  Future<void> migration({required StorageService storage}) async {
    // Mock implementation - do nothing on web
  }

  @override
  Future<void> clearDatabase() async {
    // Mock implementation - do nothing on web
  }
}

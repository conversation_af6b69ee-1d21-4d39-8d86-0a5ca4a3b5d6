// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'anime_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AnimeModelImpl _$$AnimeModelImplFromJson(Map<String, dynamic> json) =>
    _$AnimeModelImpl(
      currentPage: (json['currentPage'] as num?)?.toInt(),
      hasNextPage: json['hasNextPage'] as bool?,
      results: (json['results'] as List<dynamic>?)
          ?.map((e) => AnimeResults.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AnimeModelImplToJson(_$AnimeModelImpl instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'hasNextPage': instance.hasNextPage,
      'results': instance.results,
    };

_$AnimeResultsImpl _$$AnimeResultsImplFromJson(Map<String, dynamic> json) =>
    _$AnimeResultsImpl(
      id: json['id'] as String,
      malId: (json['malId'] as num?)?.toInt(),
      title: json['title'] == null
          ? null
          : AnimeTitle.fromJson(json['title'] as Map<String, dynamic>),
      image: json['image'] as String?,
      trailer: json['trailer'] == null
          ? null
          : AnimeTrailer.fromJson(json['trailer'] as Map<String, dynamic>),
      description: json['description'] as String?,
      status: json['status'] as String?,
      cover: json['cover'] as String?,
      rating: (json['rating'] as num?)?.toInt(),
      releaseDate: (json['releaseDate'] as num?)?.toInt(),
      genres:
          (json['genres'] as List<dynamic>?)?.map((e) => e as String).toList(),
      totalEpisodes: (json['totalEpisodes'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$AnimeResultsImplToJson(_$AnimeResultsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'malId': instance.malId,
      'title': instance.title,
      'image': instance.image,
      'trailer': instance.trailer,
      'description': instance.description,
      'status': instance.status,
      'cover': instance.cover,
      'rating': instance.rating,
      'releaseDate': instance.releaseDate,
      'genres': instance.genres,
      'totalEpisodes': instance.totalEpisodes,
      'duration': instance.duration,
      'type': instance.type,
    };

_$AnimeTitleImpl _$$AnimeTitleImplFromJson(Map<String, dynamic> json) =>
    _$AnimeTitleImpl(
      romaji: json['romaji'] as String?,
      english: json['english'] as String?,
      native: json['native'] as String?,
      userPreferred: json['userPreferred'] as String?,
    );

Map<String, dynamic> _$$AnimeTitleImplToJson(_$AnimeTitleImpl instance) =>
    <String, dynamic>{
      'romaji': instance.romaji,
      'english': instance.english,
      'native': instance.native,
      'userPreferred': instance.userPreferred,
    };

_$AnimeTrailerImpl _$$AnimeTrailerImplFromJson(Map<String, dynamic> json) =>
    _$AnimeTrailerImpl(
      id: json['id'] as String?,
      site: json['site'] as String?,
      thumbnail: json['thumbnail'] as String?,
    );

Map<String, dynamic> _$$AnimeTrailerImplToJson(_$AnimeTrailerImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'site': instance.site,
      'thumbnail': instance.thumbnail,
    };

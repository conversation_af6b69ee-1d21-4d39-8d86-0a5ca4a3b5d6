import 'package:aniggo/src/constants/constants.dart';
import 'package:aniggo/src/data/repositories/http_service.dart';
import 'package:dio/dio.dart';

class MockHttpService implements HttpService {
  @override
  String get baseUrl => Configs.baseUrl;

  @override
  Map<String, String> get headers =>
      {'accept': 'application/json', 'content-type': 'application/json'};

  @override
  Future<Map<String, dynamic>> get(String endpoint,
      {Map<String, dynamic>? queryParameters,
      CancelToken? cancelToken,
      bool forceRefresh = false}) async {
    // Mock data for different endpoints
    if (endpoint.contains('trending')) {
      return _getMockTrendingData();
    } else if (endpoint.contains('popular')) {
      return _getMockPopularData();
    } else if (endpoint.contains('advanced-search')) {
      return _getMockSearchData();
    } else if (endpoint.contains('info')) {
      return _getMockAnimeInfo();
    } else if (endpoint.contains('watch')) {
      return _getMockEpisodeUrl();
    }

    return {};
  }

  @override
  Future<dynamic> post(
    String endpoint, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    return {};
  }

  Map<String, dynamic> _getMockTrendingData() {
    return {
      "currentPage": 1,
      "hasNextPage": true,
      "results": [
        {
          "id": "21",
          "malId": 21,
          "title": {
            "romaji": "One Piece",
            "english": "One Piece",
            "native": "ワンピース"
          },
          "image": "https://picsum.photos/300/400?random=1",
          "trailer": {
            "id": "Oo3rpDVs-SK",
            "site": "youtube",
            "thumbnail": "https://i.ytimg.com/vi/Oo3rpDVs-SK/hqdefault.jpg"
          },
          "description": "Gold Roger was known as the Pirate King...",
          "status": "Ongoing",
          "cover": "https://picsum.photos/800/300?random=2",
          "rating": 89,
          "releaseDate": 1999,
          "genres": ["Action", "Adventure", "Comedy", "Drama", "Shounen"],
          "totalEpisodes": 1000,
          "duration": 24,
          "type": "TV"
        },
        {
          "id": "11061",
          "malId": 11061,
          "title": {
            "romaji": "Hunter x Hunter (2011)",
            "english": "Hunter x Hunter (2011)",
            "native": "ハンター×ハンター (2011)"
          },
          "image": "https://picsum.photos/300/400?random=3",
          "trailer": {
            "id": "d6kBeJjTGnY",
            "site": "youtube",
            "thumbnail": "https://i.ytimg.com/vi/d6kBeJjTGnY/hqdefault.jpg"
          },
          "description":
              "A Hunter is one who travels the world doing all sorts of dangerous tasks...",
          "status": "Completed",
          "cover": "https://picsum.photos/800/300?random=4",
          "rating": 94,
          "releaseDate": 2011,
          "genres": ["Action", "Adventure", "Fantasy", "Shounen"],
          "totalEpisodes": 148,
          "duration": 24,
          "type": "TV"
        }
      ]
    };
  }

  Map<String, dynamic> _getMockPopularData() {
    return {
      "currentPage": 1,
      "hasNextPage": true,
      "results": [
        {
          "id": "20958",
          "malId": 20958,
          "title": {
            "romaji": "Shingeki no Kyojin",
            "english": "Attack on Titan",
            "native": "進撃の巨人"
          },
          "image": "https://picsum.photos/300/400?random=5",
          "trailer": {
            "id": "LHtdKWJdif4",
            "site": "youtube",
            "thumbnail": "https://i.ytimg.com/vi/LHtdKWJdif4/hqdefault.jpg"
          },
          "description":
              "Several hundred years ago, humans were nearly exterminated by titans...",
          "status": "Completed",
          "cover": "https://picsum.photos/800/300?random=6",
          "rating": 84,
          "releaseDate": 2013,
          "genres": ["Action", "Drama", "Fantasy", "Shounen"],
          "totalEpisodes": 25,
          "duration": 24,
          "type": "TV"
        }
      ]
    };
  }

  Map<String, dynamic> _getMockSearchData() {
    return {
      "currentPage": 1,
      "hasNextPage": false,
      "results": [
        {
          "id": "1535",
          "malId": 1535,
          "title": {
            "romaji": "Death Note",
            "english": "Death Note",
            "native": "デスノート"
          },
          "image": "https://picsum.photos/300/400?random=7",
          "trailer": {
            "id": "NlJZ-YgAt-c",
            "site": "youtube",
            "thumbnail": "https://i.ytimg.com/vi/NlJZ-YgAt-c/hqdefault.jpg"
          },
          "description": "Light Yagami is a genius high school student...",
          "status": "Completed",
          "cover": "https://picsum.photos/800/300?random=8",
          "rating": 86,
          "releaseDate": 2006,
          "genres": ["Supernatural", "Thriller", "Psychological", "Shounen"],
          "totalEpisodes": 37,
          "duration": 23,
          "type": "TV"
        }
      ]
    };
  }

  Map<String, dynamic> _getMockAnimeInfo() {
    return {
      "id": "21",
      "title": {
        "romaji": "One Piece",
        "english": "One Piece",
        "native": "ワンピース"
      },
      "malId": 21,
      "synonyms": ["OP"],
      "isLicensed": true,
      "isAdult": false,
      "countryOfOrigin": "JP",
      "image": "https://picsum.photos/300/400?random=9",
      "cover": "https://picsum.photos/800/300?random=10",
      "popularity": 365966,
      "color": "#f1935c",
      "description": "Gold Roger was known as the Pirate King...",
      "status": "Ongoing",
      "releaseDate": 1999,
      "startDate": {"year": 1999, "month": 10, "day": 20},
      "endDate": {"year": null, "month": null, "day": null},
      "totalEpisodes": 1000,
      "currentEpisode": 1000,
      "rating": 89,
      "duration": 24,
      "genres": ["Action", "Adventure", "Comedy", "Drama", "Shounen"],
      "season": "FALL",
      "studios": ["Toei Animation"],
      "subOrDub": "sub",
      "type": "TV",
      "recommendations": [],
      "characters": [],
      "relations": [],
      "episodes": [
        {
          "id": "one-piece-episode-1",
          "title": "I'm Luffy! The Man Who's Gonna Be King of the Pirates!",
          "description": "A young boy named Monkey D. Luffy...",
          "number": 1,
          "image":
              "https://img.zorores.com/_r/300x400/100/54/90/5490cb32786d4f2e2932dc64e3d5a982/5490cb32786d4f2e2932dc64e3d5a982.jpg",
          "airDate": "1999-10-20T00:00:00.000Z"
        }
      ]
    };
  }

  Map<String, dynamic> _getMockEpisodeUrl() {
    return {
      "headers": {"Referer": "https://gogoplay1.com/"},
      "sources": [
        {
          "url":
              "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4",
          "isM3U8": false,
          "quality": "720p"
        }
      ],
      "download":
          "https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
    };
  }
}

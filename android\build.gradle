buildscript {
    ext.kotlin_version = '1.9.20' // <PERSON><PERSON><PERSON> nh<PERSON><PERSON> phiê<PERSON> bả<PERSON>
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.0' // C<PERSON><PERSON> nhật phiên bản AGP
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.layout.buildDirectory
}

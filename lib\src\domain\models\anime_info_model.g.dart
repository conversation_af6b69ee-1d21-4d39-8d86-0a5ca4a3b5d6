// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'anime_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AnimeInfoModelImpl _$$AnimeInfoModelImplFromJson(Map<String, dynamic> json) =>
    _$AnimeInfoModelImpl(
      id: json['id'] as String?,
      title: json['title'] == null
          ? null
          : AnimeTitle.fromJson(json['title'] as Map<String, dynamic>),
      malId: (json['malId'] as num?)?.toInt(),
      synonyms: (json['synonyms'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      isLicensed: json['isLicensed'] as bool?,
      isAdult: json['isAdult'] as bool?,
      countryOfOrigin: json['countryOfOrigin'] as String?,
      trailer: json['trailer'] == null
          ? null
          : AnimeTrailer.fromJson(json['trailer'] as Map<String, dynamic>),
      image: json['image'] as String?,
      popularity: (json['popularity'] as num?)?.toInt(),
      color: json['color'] as String?,
      cover: json['cover'] as String?,
      description: json['description'] as String?,
      status: json['status'] as String?,
      releaseDate: (json['releaseDate'] as num?)?.toInt(),
      startDate: json['startDate'] == null
          ? null
          : StartDate.fromJson(json['startDate'] as Map<String, dynamic>),
      endDate: json['endDate'] == null
          ? null
          : StartDate.fromJson(json['endDate'] as Map<String, dynamic>),
      totalEpisodes: (json['totalEpisodes'] as num?)?.toInt(),
      rating: (json['rating'] as num?)?.toInt(),
      duration: (json['duration'] as num?)?.toInt(),
      genres:
          (json['genres'] as List<dynamic>?)?.map((e) => e as String).toList(),
      season: json['season'] as String?,
      studios:
          (json['studios'] as List<dynamic>?)?.map((e) => e as String).toList(),
      subOrDub: json['subOrDub'] as String?,
      type: json['type'] as String?,
      recommendations: (json['recommendations'] as List<dynamic>?)
          ?.map((e) => Recommendation.fromJson(e as Map<String, dynamic>))
          .toList(),
      characters: (json['characters'] as List<dynamic>?)
          ?.map((e) => Character.fromJson(e as Map<String, dynamic>))
          .toList(),
      relations: (json['relations'] as List<dynamic>?)
          ?.map((e) => Relation.fromJson(e as Map<String, dynamic>))
          .toList(),
      episodes: (json['episodes'] as List<dynamic>?)
          ?.map((e) => Episode.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$AnimeInfoModelImplToJson(
        _$AnimeInfoModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'malId': instance.malId,
      'synonyms': instance.synonyms,
      'isLicensed': instance.isLicensed,
      'isAdult': instance.isAdult,
      'countryOfOrigin': instance.countryOfOrigin,
      'trailer': instance.trailer,
      'image': instance.image,
      'popularity': instance.popularity,
      'color': instance.color,
      'cover': instance.cover,
      'description': instance.description,
      'status': instance.status,
      'releaseDate': instance.releaseDate,
      'startDate': instance.startDate,
      'endDate': instance.endDate,
      'totalEpisodes': instance.totalEpisodes,
      'rating': instance.rating,
      'duration': instance.duration,
      'genres': instance.genres,
      'season': instance.season,
      'studios': instance.studios,
      'subOrDub': instance.subOrDub,
      'type': instance.type,
      'recommendations': instance.recommendations,
      'characters': instance.characters,
      'relations': instance.relations,
      'episodes': instance.episodes,
    };

_$StartDateImpl _$$StartDateImplFromJson(Map<String, dynamic> json) =>
    _$StartDateImpl(
      year: (json['year'] as num?)?.toInt(),
      month: (json['month'] as num?)?.toInt(),
      day: (json['day'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$StartDateImplToJson(_$StartDateImpl instance) =>
    <String, dynamic>{
      'year': instance.year,
      'month': instance.month,
      'day': instance.day,
    };

_$RecommendationImpl _$$RecommendationImplFromJson(Map<String, dynamic> json) =>
    _$RecommendationImpl(
      id: (json['id'] as num?)?.toInt(),
      malId: (json['malId'] as num?)?.toInt(),
      title: json['title'] == null
          ? null
          : AnimeTitle.fromJson(json['title'] as Map<String, dynamic>),
      status: json['status'] as String?,
      episodes: (json['episodes'] as num?)?.toInt(),
      image: json['image'] as String?,
      cover: json['cover'] as String?,
      rating: (json['rating'] as num?)?.toInt(),
      type: json['type'] as String?,
    );

Map<String, dynamic> _$$RecommendationImplToJson(
        _$RecommendationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'malId': instance.malId,
      'title': instance.title,
      'status': instance.status,
      'episodes': instance.episodes,
      'image': instance.image,
      'cover': instance.cover,
      'rating': instance.rating,
      'type': instance.type,
    };

_$CharacterImpl _$$CharacterImplFromJson(Map<String, dynamic> json) =>
    _$CharacterImpl(
      id: (json['id'] as num?)?.toInt(),
      role: json['role'] as String?,
      name: json['name'] == null
          ? null
          : VAname.fromJson(json['name'] as Map<String, dynamic>),
      image: json['image'] as String?,
      voiceActors: (json['voiceActors'] as List<dynamic>?)
          ?.map((e) => VoiceActors.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CharacterImplToJson(_$CharacterImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'role': instance.role,
      'name': instance.name,
      'image': instance.image,
      'voiceActors': instance.voiceActors,
    };

_$VoiceActorsImpl _$$VoiceActorsImplFromJson(Map<String, dynamic> json) =>
    _$VoiceActorsImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : VAname.fromJson(json['name'] as Map<String, dynamic>),
      image: json['image'] as String?,
    );

Map<String, dynamic> _$$VoiceActorsImplToJson(_$VoiceActorsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'image': instance.image,
    };

_$VAnameImpl _$$VAnameImplFromJson(Map<String, dynamic> json) => _$VAnameImpl(
      first: json['first'] as String?,
      last: json['last'] as String?,
      full: json['full'] as String?,
      native: json['native'] as String?,
      userPreferred: json['userPreferred'] as String?,
    );

Map<String, dynamic> _$$VAnameImplToJson(_$VAnameImpl instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'full': instance.full,
      'native': instance.native,
      'userPreferred': instance.userPreferred,
    };

_$RelationImpl _$$RelationImplFromJson(Map<String, dynamic> json) =>
    _$RelationImpl(
      id: (json['id'] as num?)?.toInt(),
      relationType: json['relationType'] as String?,
      malId: (json['malId'] as num?)?.toInt(),
      title: json['title'] == null
          ? null
          : AnimeTitle.fromJson(json['title'] as Map<String, dynamic>),
      status: json['status'] as String?,
      episodes: (json['episodes'] as num?)?.toInt(),
      image: json['image'] as String?,
      color: json['color'] as String?,
      type: json['type'] as String?,
      cover: json['cover'] as String?,
      rating: (json['rating'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$RelationImplToJson(_$RelationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'relationType': instance.relationType,
      'malId': instance.malId,
      'title': instance.title,
      'status': instance.status,
      'episodes': instance.episodes,
      'image': instance.image,
      'color': instance.color,
      'type': instance.type,
      'cover': instance.cover,
      'rating': instance.rating,
    };

_$EpisodeImpl _$$EpisodeImplFromJson(Map<String, dynamic> json) =>
    _$EpisodeImpl(
      id: json['id'] as String?,
      title: json['title'] as String?,
      image: json['image'] as String?,
      number: (json['number'] as num?)?.toInt(),
      description: json['description'] as String?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$$EpisodeImplToJson(_$EpisodeImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'image': instance.image,
      'number': instance.number,
      'description': instance.description,
      'url': instance.url,
    };
